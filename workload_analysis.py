# -*- coding: utf-8 -*-
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# Set Chinese font for matplotlib
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# Read Excel file with proper encoding
file_path = '人员年度工作量统计表.xlsx'
print(f"Reading Excel file: {file_path}\n")

# Read the Excel file
df = pd.read_excel(file_path, sheet_name=0, header=None)
print("Raw data shape:", df.shape)
print("\nFirst 20 rows of raw data:")
print(df.head(20))
print("\n" + "="*80)

# Identify the actual header rows and data structure
# Based on the preview, it seems the data has merged cells and complex structure
# Let's analyze the structure more carefully

# Find rows with actual data
data_rows = []
header_info = []

for idx, row in df.iterrows():
    non_null_count = row.notna().sum()
    if non_null_count > 0:
        print(f"Row {idx}: {non_null_count} non-null values")
        print(row[row.notna()].values)
        print()

print("\n" + "="*80)
print("Analyzing data structure...")

# Try to identify administrative vs non-administrative personnel
# and extract workload data
admin_data = []
non_admin_data = []

# Extract all numeric data from the dataframe
numeric_data = []
for col in df.columns:
    for val in df[col]:
        if pd.notna(val) and isinstance(val, (int, float)):
            numeric_data.append(val)

print(f"\nFound {len(numeric_data)} numeric values in the data")
print(f"Numeric values: {numeric_data[:20]}...")  # Show first 20 values

# Save the raw data for further analysis
df.to_csv('raw_data_export.csv', index=False, encoding='utf-8-sig')
print("\nRaw data exported to 'raw_data_export.csv' for inspection")