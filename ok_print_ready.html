<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人员年度工作量交互式分析报告</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- Chosen Palette: Warm Neutrals (Slate Gray, Muted Teal) -->
    <!-- Application Structure Plan: 采用单页叙事结构，引导用户从宏观对比到微观分解，最终聚焦于核心问题——人员配置的合理性。1. 概览：通过关键指标卡片，快速呈现两类人员的核心差异。2. 深度分解：利用交互式图表，详细展示非行政人员复杂的工作构成。3. 核心论证：通过可视化的计算流程，清晰论证8人配置的科学依据。4. 结论：总结发现并提出建议。该结构旨在将复杂的分析过程转化为一个清晰、有说服力的故事，便于决策者理解和采纳。 -->
    <!-- Visualization & Content Choices: 1. 关键指标卡片 (HTML/Tailwind): 用于“概览”部分，目标是快速告知核心对比数据（人均工时、工作模式），直观高效。2. 条形图 (Chart.js): 用于“概览”部分，目标是“比较”人均年工作量的具体数值差异，条形图是此场景最清晰的可视化方法。3. 甜甜圈图 (Chart.js): 用于“深度分解”部分，目标是“组织/展示构成”，清晰地显示非行政人员各项工作（特别是“顶班”）在总工作量中的占比，交互式悬停提示可提供精确数值。4. 流程图 (HTML/Tailwind): 用于“核心论证”部分，目标是“解释/论证”8人配置的计算逻辑，将公式化的计算过程转化为易于理解的视觉步骤。所有选择均未使用SVG/Mermaid。 -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f8fafc; /* slate-50 */
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            height: 320px;
            max-height: 400px;
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 400px;
            }
        }
        .metric-card {
            background-color: white;
            border-radius: 0.75rem;
            padding: 1.5rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            transition: transform 0.2s, box-shadow 0.2s;
            border-left: 4px solid #0d9488; /* teal-600 */
        }
        .metric-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        }
        .section-title {
            font-size: 1.875rem;
            font-weight: 700;
            color: #1e293b; /* slate-800 */
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #0d9488; /* teal-600 */
        }
        .nav-button {
            background-color: #f1f5f9; /* slate-100 */
            color: #334155; /* slate-700 */
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 9999px;
            transition: background-color 0.2s, color 0.2s;
        }
        .nav-button.active {
            background-color: #0d9488; /* teal-600 */
            color: white;
        }
    </style>

    <style>
        @media print {
            /* 确保颜色在PDF中显示 */
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            
            /* 页面设置 */
            @page {
                size: A4;
                margin: 2cm;
            }
            
            /* 隐藏导航 */
            header nav {
                display: none !important;
            }
            
            /* 优化章节分页 */
            section {
                page-break-inside: avoid;
                break-inside: avoid;
                margin-bottom: 2rem;
            }
            
            /* 确保图表容器适合页面 */
            .chart-container {
                page-break-inside: avoid;
                break-inside: avoid;
                max-height: 400px !important;
                margin: 1rem 0;
            }
            
            /* 优化表格 */
            .metric-card {
                page-break-inside: avoid;
                break-inside: avoid;
                margin-bottom: 1rem;
            }
            
            /* 确保Canvas图表可见 */
            canvas {
                max-width: 100% !important;
                height: auto !important;
                page-break-inside: avoid;
            }
            
            /* 优化字体大小 */
            .section-title {
                font-size: 1.5rem !important;
                margin-bottom: 1rem !important;
            }
            
            /* 优化间距 */
            .mb-16 {
                margin-bottom: 2rem !important;
            }
            
            /* 确保背景色显示 */
            .bg-white {
                background-color: white !important;
            }
            
            .bg-teal-600 {
                background-color: #0d9488 !important;
            }
            
            .text-white {
                color: white !important;
            }
            
            /* 优化表格显示 */
            table {
                border-collapse: collapse !important;
                width: 100% !important;
            }
            
            th, td {
                border: 1px solid #ccc !important;
                padding: 8px !important;
                text-align: center !important;
            }
            
            th {
                background-color: #0d9488 !important;
                color: white !important;
            }
        }
        
        /* 添加页面计数器 */
        @page {
            @bottom-right {
                content: "第 " counter(page) " 页";
                font-size: 10pt;
                color: #666;
            }
            
            @top-center {
                content: "人员年度工作量交互式分析报告";
                font-size: 12pt;
                color: #666;
                font-weight: bold;
            }
        }
        
        /* 确保页面在屏幕上也能正常显示 */
        body {
            font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif;
            line-height: 1.6;
        }
    </style>
    
    <script>
        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成');
            
            // 等待图表加载完成
            setTimeout(function() {
                console.log('图表应该已经加载完成');
                
                // 添加打印就绪标记
                document.body.setAttribute('data-print-ready', 'true');
                
                // 在控制台显示打印提示
                console.log('页面已准备好打印！');
                console.log('请按 Ctrl+P 打印，或在浏览器菜单中选择"打印"');
                console.log('建议选择"另存为PDF"作为打印机');
                
            }, 3000);
        });
        
        // 添加打印按钮
        window.addEventListener('load', function() {
            // 创建打印按钮
            const printButton = document.createElement('button');
            printButton.innerHTML = '🖨️ 打印/保存为PDF';
            printButton.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
                padding: 10px 20px;
                background-color: #0d9488;
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                font-size: 14px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            `;
            
            printButton.onclick = function() {
                window.print();
            };
            
            // 只在非打印模式下显示按钮
            const style = document.createElement('style');
            style.textContent = '@media print { .print-button { display: none !important; } }';
            document.head.appendChild(style);
            printButton.className = 'print-button';
            
            document.body.appendChild(printButton);
        });
    </script>
    
</head>
<body class="text-slate-700">

    <header class="bg-white shadow-md sticky top-0 z-10">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <h1 class="text-xl md:text-2xl font-bold text-slate-800">人员年度工作量交互式分析报告</h1>
            <nav class="hidden md:flex space-x-4">
                <a href="#summary" class="nav-button active">核心概览</a>
                <a href="#breakdown" class="nav-button">工作量分解</a>
                <a href="#justification" class="nav-button">人员配置论证</a>
                <a href="#conclusion" class="nav-button">结论与建议</a>
            </nav>
        </div>
    </header>

    <main class="container mx-auto p-4 md:p-8">

        <section id="summary" class="mb-16 scroll-mt-24">
            <h2 class="section-title">核心概览：行政与非行政人员工作量对比</h2>
            <p class="text-lg text-slate-600 mb-8 max-w-4xl mx-auto text-center">
                本部分旨在快速揭示行政与非行政人员在工作负荷上的核心差异。通过关键指标和可视化图表的对比，您可以直观地了解两类岗位在工作量、工作模式及运营贡献上的根本不同。
            </p>

            <div class="grid md:grid-cols-3 gap-6 mb-12">
                <div class="metric-card md:col-span-1">
                    <h3 class="text-lg font-semibold text-slate-800 mb-2">人均年工作量</h3>
                    <p class="text-3xl font-bold text-teal-600">9.5% <span class="text-lg font-normal text-slate-500">更高</span></p>
                    <p class="mt-2 text-slate-500">非行政人员的人均年工作量（1779小时）显著高于行政人员（1625小时）。</p>
                </div>
                <div class="metric-card md:col-span-1">
                    <h3 class="text-lg font-semibold text-slate-800 mb-2">运营模式</h3>
                    <p class="text-3xl font-bold text-teal-600">24/7 <span class="text-lg font-normal text-slate-500">连续运转</span></p>
                    <p class="mt-2 text-slate-500">非行政岗位需保障全天候不间断服务，实行复杂的轮班制度。</p>
                </div>
                <div class="metric-card md:col-span-1">
                    <h3 class="text-lg font-semibold text-slate-800 mb-2">工作复杂度</h3>
                    <p class="text-3xl font-bold text-teal-600">>20% <span class="text-lg font-normal text-slate-500">为顶班工时</span></p>
                    <p class="mt-2 text-slate-500">非行政工作包含大量顶班、培训等辅助任务，以确保运营韧性。</p>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-lg">
                <h3 class="text-xl font-semibold text-slate-800 text-center mb-4">人均年度工作量（小时）对比</h3>
                <div class="chart-container">
                    <canvas id="comparisonChart"></canvas>
                </div>
                 <p class="text-center text-sm text-slate-500 mt-4">此图表直观展示了非行政人员在年度总工时上承担了更高的负荷。点击图例可隐藏或显示对应数据。</p>
            </div>
        </section>

        <section id="breakdown" class="mb-16 scroll-mt-24">
            <h2 class="section-title">深度分解：非行政人员工作量构成</h2>
            <p class="text-lg text-slate-600 mb-8 max-w-4xl mx-auto text-center">
                非行政人员的工作远不止于常规值守。本节将深入剖析其年度总工作量的具体构成，揭示各项任务的占比。请特别关注“顶班”部分，它是保障24/7运营连续性和员工福祉的关键缓冲。
            </p>
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <h3 class="text-xl font-semibold text-slate-800 text-center mb-4">非行政人员年度总工作量 (14,233小时) 构成分析</h3>
                <div class="chart-container">
                    <canvas id="breakdownChart"></canvas>
                </div>
                <p class="text-center text-sm text-slate-500 mt-4">将鼠标悬停在图表板块上，可查看各部分工时的具体数值和占比。超过20%的“顶班”工时凸显了维持运营韧性所需的人员储备。</p>
            </div>
        </section>

        <section id="justification" class="mb-16 scroll-mt-24">
            <h2 class="section-title">核心论证：为何需要8名非行政人员？</h2>
            <p class="text-lg text-slate-600 mb-8 max-w-4xl mx-auto text-center">
                8人配置并非随意设定，而是基于总工作需求和可持续工作负荷的科学计算结果。本节将通过可视化的计算流程，清晰展示从总工时需求到最低人员配置的逻辑推导过程。
            </p>
            <div class="bg-white p-8 rounded-lg shadow-lg">
                <div class="flex flex-col md:flex-row items-center justify-center space-y-6 md:space-y-0 md:space-x-6">
                    <div class="text-center p-6 bg-slate-50 rounded-lg border border-slate-200">
                        <h4 class="text-lg font-semibold text-slate-700">年度总工作需求</h4>
                        <p class="text-4xl font-bold text-teal-700 my-2">14,233</p>
                        <p class="text-slate-500">小时</p>
                        <p class="text-xs text-slate-400 mt-2">(值守+培训+顶班等)</p>
                    </div>

                    <div class="text-5xl font-thin text-slate-400">÷</div>

                    <div class="text-center p-6 bg-slate-50 rounded-lg border border-slate-200">
                        <h4 class="text-lg font-semibold text-slate-700">人均可持续年工时</h4>
                        <p class="text-4xl font-bold text-teal-700 my-2">1,779</p>
                        <p class="text-slate-500">小时</p>
                        <p class="text-xs text-slate-400 mt-2">(平衡高强度与休息)</p>
                    </div>

                    <div class="text-5xl font-thin text-slate-400">=</div>
                    
                    <div class="text-center p-6 bg-teal-600 text-white rounded-lg shadow-xl">
                        <h4 class="text-lg font-semibold">最低人员配置</h4>
                        <p class="text-4xl font-bold my-2">8</p>
                        <p class="">人</p>
                        <p class="text-xs text-teal-100 mt-2">(科学验证的必要数量)</p>
                    </div>
                </div>
                <div class="mt-8 pt-6 border-t border-slate-200">
                    <h4 class="text-xl font-semibold text-slate-800 mb-4">论证逻辑：</h4>
                    <ul class="space-y-4 text-slate-600">
                        <li class="flex items-start">
                            <span class="text-teal-500 font-bold mr-3">✔</span>
                            <div><strong class="text-slate-700">保障24/7覆盖：</strong>14,233小时的总工作量远超任何个人能力，必须由团队完成。8人配置是实现无间断轮班和节假日值守的基础。</div>
                        </li>
                        <li class="flex items-start">
                             <span class="text-teal-500 font-bold mr-3">✔</span>
                            <div><strong class="text-slate-700">吸收必要缺勤：</strong>“顶班”所需的2880小时，加上培训、休假等，必须由足够的人员储备来分摊。减少人员将导致服务中断或现有员工过度劳累。</div>
                        </li>
                        <li class="flex items-start">
                             <span class="text-teal-500 font-bold mr-3">✔</span>
                            <div><strong class="text-slate-700">维持工作负荷平衡：</strong>1779小时的人均年工作量是高强度轮班工作下的合理负荷。少于8人将推高人均工时，引发疲劳、效率下降和安全风险，最终导致运营不可持续。</div>
                        </li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="conclusion" class="scroll-mt-24">
            <h2 class="section-title">结论与建议</h2>
             <p class="text-lg text-slate-600 mb-8 max-w-4xl mx-auto text-center">
                综合以上分析，本报告得出明确结论并提出具体建议，旨在为管理层提供数据驱动的决策支持，以确保组织的长期稳定、高效运营。
            </p>
            <div class="bg-white p-8 rounded-lg shadow-lg">
                <h3 class="text-xl font-semibold text-slate-800 mb-4">核心结论</h3>
                <p class="text-slate-600 mb-6">
                    数据分析明确证明，非行政人员的工作负荷不仅在数量上（高出9.5%），更在性质上（24/7轮班、高强度、高复杂度）远超行政人员。当前<strong class="text-teal-600">8名非行政人员的配置是经过科学计算验证的最低需求</strong>，是保障组织核心业务不间断运行、维持运营韧性、并确保员工福祉的基石。任何削减都将对运营稳定性和服务质量构成直接威胁。
                </p>
                <h3 class="text-xl font-semibold text-slate-800 mb-4">管理建议</h3>
                <ul class="space-y-3 list-disc list-inside text-slate-600">
                    <li><strong class="text-slate-700">维持当前配置：</strong>坚决维持8名非行政人员的配置，将其视为保障组织核心竞争力的战略性投资。</li>
                    <li><strong class="text-slate-700">定期工作量审查：</strong>建立年度工作量审查机制，动态评估运营需求，确保人员配置始终与实际情况相匹配。</li>
                    <li><strong class="text-slate-700">加强员工关怀：</strong>针对非行政人员高强度的工作特性，提供专项的疲劳管理和心理健康支持，提升员工满意度和留存率。</li>
                </ul>
            </div>
        </section>

    </main>

    <footer class="bg-slate-800 text-slate-300 mt-16">
        <div class="container mx-auto p-4 text-center text-sm">
            <p>&copy; 2024 年度工作量分析报告</p>
            <p class="text-xs text-slate-400 mt-1">此报告由数据驱动生成，旨在提供客观、清晰的决策支持。</p>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const data = {
                admin: {
                    name: '行政人员',
                    avg_hours: 1625,
                },
                non_admin: {
                    name: '非行政人员',
                    avg_hours: 1779.125,
                    breakdown: {
                        labels: ['工作日值守', '节假日值守', '顶班', '会议培训', '党建学习', '训练演练'],
                        hours: [8500, 2784, 2880, 36, 24, 9],
                        total: 14233,
                    }
                }
            };

            const colors = {
                teal: '#0d9488',
                slate: '#64748b',
                lightTeal: '#5eead4',
                lightSlate: '#cbd5e1',
                breakdown: ['#14b8a6', '#2dd4bf', '#f97316', '#a8a29e', '#d6d3d1', '#e7e5e4']
            };
            
            Chart.defaults.font.family = "'Noto Sans SC', sans-serif";
            Chart.defaults.color = '#334155';

            function formatLabel(str) {
                const maxLength = 16;
                if (str.length <= maxLength) return str;
                return str.match(new RegExp(`.{1,${maxLength}}`, 'g'));
            }

            const comparisonCtx = document.getElementById('comparisonChart').getContext('2d');
            new Chart(comparisonCtx, {
                type: 'bar',
                data: {
                    labels: [data.non_admin.name, data.admin.name],
                    datasets: [{
                        label: '人均年度工作量 (小时)',
                        data: [data.non_admin.avg_hours, data.admin.avg_hours],
                        backgroundColor: [colors.teal, colors.slate],
                        borderColor: [colors.teal, colors.slate],
                        borderWidth: 1,
                        borderRadius: 4,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    scales: {
                        x: {
                            beginAtZero: true,
                            grid: {
                                color: '#e2e8f0'
                            },
                            title: {
                                display: true,
                                text: '小时'
                            }
                        },
                        y: {
                           grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return ` ${context.dataset.label}: ${context.raw.toFixed(1)} 小时`;
                                }
                            }
                        }
                    }
                }
            });

            const breakdownCtx = document.getElementById('breakdownChart').getContext('2d');
            new Chart(breakdownCtx, {
                type: 'doughnut',
                data: {
                    labels: data.non_admin.breakdown.labels,
                    datasets: [{
                        label: '工时',
                        data: data.non_admin.breakdown.hours,
                        backgroundColor: colors.breakdown,
                        hoverOffset: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw;
                                    const percentage = ((value / data.non_admin.breakdown.total) * 100).toFixed(1);
                                    return `${label}: ${value} 小时 (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });

            const navLinks = document.querySelectorAll('.nav-button');
            const sections = document.querySelectorAll('section');

            function changeNav() {
                let index = sections.length;
                while(--index && window.scrollY + 100 < sections[index].offsetTop) {}
                
                navLinks.forEach((link) => link.classList.remove('active'));
                if(navLinks[index]) {
                   navLinks[index].classList.add('active');
                }
            }

            changeNav();
            window.addEventListener('scroll', changeNav);

            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    const targetId = link.getAttribute('href');
                    const targetElement = document.querySelector(targetId);
                    if (targetElement) {
                        window.scrollTo({
                            top: targetElement.offsetTop - 80,
                            behavior: 'smooth'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>
