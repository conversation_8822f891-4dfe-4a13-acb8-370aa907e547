#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Excel文件读取和分析脚本
"""

import pandas as pd
import numpy as np

def read_excel_simple(file_path):
    """简单读取Excel文件"""
    try:
        print(f"正在读取文件: {file_path}")
        
        # 读取Excel文件的所有工作表
        excel_file = pd.ExcelFile(file_path)
        print(f"工作表列表: {excel_file.sheet_names}")
        
        # 读取第一个工作表
        sheet_name = excel_file.sheet_names[0]
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
        
        print(f"\n工作表 '{sheet_name}' 的数据:")
        print(f"数据形状: {df.shape}")
        print("\n完整数据内容:")
        
        # 显示所有数据
        for i, row in df.iterrows():
            print(f"第{i+1}行: {list(row.values)}")
        
        return df
        
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

def analyze_data_manually(df):
    """手动分析数据"""
    print("\n" + "="*60)
    print("手动数据分析")
    print("="*60)
    
    # 查找包含"表一"和"表二"的行
    table1_rows = []
    table2_rows = []
    
    for i, row in df.iterrows():
        row_text = ' '.join([str(val) for val in row.values if pd.notna(val)])
        if '表一' in row_text:
            print(f"找到表一标题在第{i+1}行: {row_text}")
            table1_rows.append(i)
        elif '表二' in row_text:
            print(f"找到表二标题在第{i+1}行: {row_text}")
            table2_rows.append(i)
    
    # 提取数值数据
    all_numbers = []
    table1_numbers = []
    table2_numbers = []
    
    for i, row in df.iterrows():
        for val in row.values:
            if pd.notna(val):
                try:
                    # 尝试转换为数值
                    str_val = str(val).replace(',', '').replace('小时', '').replace('天', '').replace('周', '')
                    num_val = float(str_val)
                    if num_val > 0:  # 只保留正数
                        all_numbers.append(num_val)
                        print(f"第{i+1}行发现数值: {num_val}")
                        
                        # 根据位置判断属于哪个表
                        if table1_rows and i > min(table1_rows):
                            if not table2_rows or i < min(table2_rows):
                                table1_numbers.append(num_val)
                        elif table2_rows and i > min(table2_rows):
                            table2_numbers.append(num_val)
                            
                except:
                    pass
    
    print(f"\n提取到的所有数值: {all_numbers}")
    print(f"表一相关数值: {table1_numbers}")
    print(f"表二相关数值: {table2_numbers}")
    
    # 计算统计指标
    if table1_numbers:
        print(f"\n表一（非行政人员）统计:")
        print(f"  总量: {sum(table1_numbers):.2f}")
        print(f"  平均值: {np.mean(table1_numbers):.2f}")
        print(f"  中位数: {np.median(table1_numbers):.2f}")
        print(f"  最大值: {max(table1_numbers):.2f}")
        print(f"  最小值: {min(table1_numbers):.2f}")
        print(f"  数据点数: {len(table1_numbers)}")
    
    if table2_numbers:
        print(f"\n表二（行政人员）统计:")
        print(f"  总量: {sum(table2_numbers):.2f}")
        print(f"  平均值: {np.mean(table2_numbers):.2f}")
        print(f"  中位数: {np.median(table2_numbers):.2f}")
        print(f"  最大值: {max(table2_numbers):.2f}")
        print(f"  最小值: {min(table2_numbers):.2f}")
        print(f"  数据点数: {len(table2_numbers)}")
    
    return {
        'table1_numbers': table1_numbers,
        'table2_numbers': table2_numbers,
        'all_numbers': all_numbers
    }

def calculate_staff_requirements(analysis_data):
    """计算人员配备需求"""
    print("\n" + "="*60)
    print("人员配备需求分析")
    print("="*60)
    
    table1_numbers = analysis_data['table1_numbers']
    table2_numbers = analysis_data['table2_numbers']
    
    if not table1_numbers:
        print("缺少非行政人员数据，无法进行分析")
        return
    
    # 非行政人员数据分析
    total_workload = sum(table1_numbers)
    avg_workload = np.mean(table1_numbers)
    
    print(f"非行政人员工作量分析:")
    print(f"  总工作量: {total_workload:.2f}")
    print(f"  平均工作量: {avg_workload:.2f}")
    
    # 估算当前人员数量（假设每个数据点代表一个工作项或时间段）
    estimated_current_staff = max(1, len(table1_numbers) // 3)  # 保守估计
    current_load_per_person = total_workload / estimated_current_staff
    
    print(f"  估算当前人员: {estimated_current_staff}人")
    print(f"  当前人均负荷: {current_load_per_person:.2f}")
    
    # 设定合理工作负荷标准
    if table2_numbers:
        admin_avg = np.mean(table2_numbers)
        reasonable_load = admin_avg
        print(f"  行政人员平均工作量: {admin_avg:.2f}")
        print(f"  以此作为合理负荷标准: {reasonable_load:.2f}")
    else:
        reasonable_load = avg_workload * 0.8  # 降低20%作为合理标准
        print(f"  设定合理负荷标准(平均值的80%): {reasonable_load:.2f}")
    
    # 计算最少需求
    min_required = total_workload / reasonable_load
    print(f"  基础最少需求: {min_required:.1f}人")
    
    # 考虑效率和应急因素
    efficiency_buffer = 1.2  # 20%效率缓冲
    emergency_buffer = 1.1   # 10%应急缓冲
    
    recommended = min_required * efficiency_buffer * emergency_buffer
    print(f"  考虑效率缓冲(+20%): {min_required * efficiency_buffer:.1f}人")
    print(f"  考虑应急缓冲(+10%): {recommended:.1f}人")
    
    print(f"\n结论:")
    if recommended >= 8:
        print(f"  ✓ 建议配备 {recommended:.0f} 人")
        print(f"  ✓ 支持非行政人员配备不少于8人的要求")
    else:
        print(f"  ⚠ 基于当前数据，建议配备 {recommended:.0f} 人")
        print(f"  ⚠ 如需满足8人要求，可能需要重新评估工作分配")
    
    return {
        'total_workload': total_workload,
        'recommended_staff': recommended,
        'min_required': min_required
    }

def generate_simple_report(df, analysis_data, staff_requirements):
    """生成简单报告"""
    report_lines = []
    
    report_lines.append("# 人员年度工作量统计分析报告")
    report_lines.append("")
    report_lines.append("## 数据来源")
    report_lines.append(f"- 文件: 人员年度工作量统计表.xlsx")
    report_lines.append(f"- 数据行数: {len(df)}")
    report_lines.append(f"- 数据列数: {len(df.columns)}")
    report_lines.append("")
    
    # 数据分析结果
    table1_numbers = analysis_data['table1_numbers']
    table2_numbers = analysis_data['table2_numbers']
    
    if table1_numbers:
        report_lines.append("## 非行政人员工作量分析")
        report_lines.append(f"- 总工作量: {sum(table1_numbers):.2f}")
        report_lines.append(f"- 平均工作量: {np.mean(table1_numbers):.2f}")
        report_lines.append(f"- 最大工作量: {max(table1_numbers):.2f}")
        report_lines.append(f"- 最小工作量: {min(table1_numbers):.2f}")
        report_lines.append(f"- 数据点数: {len(table1_numbers)}")
        report_lines.append("")
    
    if table2_numbers:
        report_lines.append("## 行政人员工作量分析")
        report_lines.append(f"- 总工作量: {sum(table2_numbers):.2f}")
        report_lines.append(f"- 平均工作量: {np.mean(table2_numbers):.2f}")
        report_lines.append(f"- 最大工作量: {max(table2_numbers):.2f}")
        report_lines.append(f"- 最小工作量: {min(table2_numbers):.2f}")
        report_lines.append(f"- 数据点数: {len(table2_numbers)}")
        report_lines.append("")
    
    # 人员配备建议
    if staff_requirements:
        report_lines.append("## 人员配备建议")
        report_lines.append(f"- 建议非行政人员配备: {staff_requirements['recommended_staff']:.0f}人")
        report_lines.append(f"- 最少需求: {staff_requirements['min_required']:.1f}人")
        report_lines.append(f"- 总工作量: {staff_requirements['total_workload']:.2f}")
        report_lines.append("")
        
        if staff_requirements['recommended_staff'] >= 8:
            report_lines.append("**结论**: 分析结果支持非行政人员配备不少于8人的要求。")
        else:
            report_lines.append(f"**结论**: 基于当前数据，建议配备{staff_requirements['recommended_staff']:.0f}人。")
        report_lines.append("")
    
    # 保存报告
    report_content = '\n'.join(report_lines)
    with open('simple_analysis_report.md', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print("\n报告已保存为: simple_analysis_report.md")
    return report_content

def main():
    """主函数"""
    file_path = "人员年度工作量统计表.xlsx"
    
    print("开始分析Excel文件...")
    
    # 1. 读取数据
    df = read_excel_simple(file_path)
    if df is None:
        return
    
    # 2. 手动分析数据
    analysis_data = analyze_data_manually(df)
    
    # 3. 计算人员配备需求
    staff_requirements = calculate_staff_requirements(analysis_data)
    
    # 4. 生成报告
    report = generate_simple_report(df, analysis_data, staff_requirements)
    
    print("\n" + "="*60)
    print("分析完成!")
    print("="*60)

if __name__ == "__main__":
    main()
