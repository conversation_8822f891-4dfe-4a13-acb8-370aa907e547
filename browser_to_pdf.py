#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用浏览器将HTML转换为包含图表的PDF
"""

import os
import sys
import subprocess
import time
import j<PERSON>

def install_playwright():
    """安装Playwright"""
    try:
        import playwright
        print("✓ Playwright 已安装")
        return True
    except ImportError:
        print("正在安装 Playwright...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'playwright'])
            print("正在安装浏览器...")
            subprocess.check_call([sys.executable, '-m', 'playwright', 'install', 'chromium'])
            print("✓ Playwright 安装完成")
            return True
        except Exception as e:
            print(f"✗ Playwright 安装失败: {e}")
            return False

def create_print_optimized_html():
    """创建打印优化的HTML版本"""
    
    # 读取原始HTML
    with open('ok.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 添加打印优化的CSS和JavaScript
    print_optimization = """
    <style>
        @media print {
            /* 确保颜色在PDF中显示 */
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            
            /* 页面设置 */
            @page {
                size: A4;
                margin: 1.5cm;
            }
            
            /* 隐藏导航 */
            header nav {
                display: none !important;
            }
            
            /* 优化章节分页 */
            section {
                page-break-inside: avoid;
                break-inside: avoid;
                margin-bottom: 1.5rem;
            }
            
            /* 确保图表容器适合页面 */
            .chart-container {
                page-break-inside: avoid;
                break-inside: avoid;
                max-height: 300px !important;
                margin: 1rem 0;
            }
            
            /* 优化表格 */
            .metric-card {
                page-break-inside: avoid;
                break-inside: avoid;
                margin-bottom: 1rem;
            }
            
            /* 确保Canvas图表可见 */
            canvas {
                max-width: 100% !important;
                height: auto !important;
                page-break-inside: avoid;
            }
            
            /* 优化字体大小 */
            .section-title {
                font-size: 1.4rem !important;
                margin-bottom: 1rem !important;
            }
            
            /* 优化间距 */
            .mb-16 {
                margin-bottom: 2rem !important;
            }
            
            /* 确保背景色显示 */
            .bg-white {
                background-color: white !important;
            }
            
            .bg-teal-600 {
                background-color: #0d9488 !important;
            }
            
            .text-white {
                color: white !important;
            }
        }
        
        /* 添加页面计数器 */
        @page {
            @bottom-right {
                content: "第 " counter(page) " 页";
                font-size: 10pt;
                color: #666;
            }
            
            @top-center {
                content: "人员年度工作量交互式分析报告";
                font-size: 10pt;
                color: #666;
            }
        }
    </style>
    
    <script>
        // 确保图表在打印前完全加载
        window.addEventListener('beforeprint', function() {
            // 等待图表渲染完成
            setTimeout(function() {
                console.log('Charts should be ready for printing');
            }, 1000);
        });
        
        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            // 等待图表加载完成
            setTimeout(function() {
                console.log('Page fully loaded with charts');
                // 添加打印就绪标记
                document.body.setAttribute('data-print-ready', 'true');
            }, 3000);
        });
    </script>
    """
    
    # 在</head>前插入优化代码
    optimized_html = html_content.replace('</head>', print_optimization + '\n</head>')
    
    # 保存优化版HTML
    with open('ok_print_optimized.html', 'w', encoding='utf-8') as f:
        f.write(optimized_html)
    
    print("✓ 创建打印优化HTML文件: ok_print_optimized.html")
    return 'ok_print_optimized.html'

def convert_with_playwright(html_file):
    """使用Playwright转换为PDF"""
    try:
        from playwright.sync_api import sync_playwright
        
        print("正在使用Playwright转换为PDF...")
        
        with sync_playwright() as p:
            # 启动浏览器
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            
            # 设置视口大小
            page.set_viewport_size({"width": 1200, "height": 800})
            
            # 获取文件的绝对路径
            html_path = os.path.abspath(html_file)
            file_url = f"file:///{html_path.replace(os.sep, '/')}"
            
            print(f"加载页面: {file_url}")
            
            # 加载页面
            page.goto(file_url, wait_until='networkidle')
            
            # 等待图表加载完成
            print("等待图表渲染...")
            
            # 等待Canvas元素出现
            page.wait_for_selector('canvas', timeout=10000)
            
            # 额外等待确保图表完全渲染
            time.sleep(3)
            
            # 检查是否有打印就绪标记
            try:
                page.wait_for_function(
                    "document.body.getAttribute('data-print-ready') === 'true'",
                    timeout=5000
                )
                print("✓ 页面打印就绪")
            except:
                print("⚠ 页面可能未完全就绪，继续生成PDF")
            
            # 生成PDF
            pdf_file = 'ok_with_charts.pdf'
            
            page.pdf(
                path=pdf_file,
                format='A4',
                print_background=True,
                margin={
                    'top': '1.5cm',
                    'right': '1.5cm',
                    'bottom': '1.5cm',
                    'left': '1.5cm'
                },
                display_header_footer=True,
                header_template='<div style="font-size:10px; text-align:center; width:100%; margin-top:0.5cm;">人员年度工作量交互式分析报告</div>',
                footer_template='<div style="font-size:10px; text-align:center; width:100%; margin-bottom:0.5cm;"><span class="pageNumber"></span> / <span class="totalPages"></span></div>'
            )
            
            browser.close()
            
        print(f"✓ Playwright转换完成: {pdf_file}")
        return pdf_file
        
    except Exception as e:
        print(f"✗ Playwright转换失败: {e}")
        return None

def convert_with_pyppeteer():
    """使用pyppeteer转换为PDF（备选方案）"""
    try:
        import asyncio
        from pyppeteer import launch
        
        print("正在使用pyppeteer转换为PDF...")
        
        async def generate_pdf():
            browser = await launch(headless=True, args=['--no-sandbox'])
            page = await browser.newPage()
            
            # 设置视口
            await page.setViewport({'width': 1200, 'height': 800})
            
            # 获取文件路径
            html_path = os.path.abspath('ok_print_optimized.html')
            file_url = f"file:///{html_path.replace(os.sep, '/')}"
            
            # 加载页面
            await page.goto(file_url, {'waitUntil': 'networkidle0'})
            
            # 等待图表加载
            await page.waitForSelector('canvas', {'timeout': 10000})
            await asyncio.sleep(3)
            
            # 生成PDF
            pdf_file = 'ok_pyppeteer.pdf'
            await page.pdf({
                'path': pdf_file,
                'format': 'A4',
                'printBackground': True,
                'margin': {
                    'top': '1.5cm',
                    'right': '1.5cm',
                    'bottom': '1.5cm',
                    'left': '1.5cm'
                }
            })
            
            await browser.close()
            return pdf_file
        
        # 运行异步函数
        pdf_file = asyncio.get_event_loop().run_until_complete(generate_pdf())
        
        print(f"✓ pyppeteer转换完成: {pdf_file}")
        return pdf_file
        
    except Exception as e:
        print(f"✗ pyppeteer转换失败: {e}")
        return None

def main():
    """主函数"""
    print("="*60)
    print("浏览器HTML转PDF工具（包含图表）")
    print("="*60)
    
    # 检查输入文件
    if not os.path.exists('ok.html'):
        print("✗ 错误: 找不到ok.html文件")
        return
    
    print(f"\n开始转换HTML文件为包含图表的PDF...")
    
    # 创建打印优化的HTML
    optimized_html = create_print_optimized_html()
    
    pdf_files = []
    
    # 方法1: 尝试使用Playwright
    if install_playwright():
        pdf_file = convert_with_playwright(optimized_html)
        if pdf_file:
            pdf_files.append(pdf_file)
    
    # 方法2: 尝试安装和使用pyppeteer作为备选
    if not pdf_files:
        try:
            print("尝试安装pyppeteer作为备选方案...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyppeteer'])
            pdf_file = convert_with_pyppeteer()
            if pdf_file:
                pdf_files.append(pdf_file)
        except:
            print("✗ pyppeteer安装或使用失败")
    
    # 总结
    print(f"\n" + "="*60)
    print("转换完成！")
    print("="*60)
    
    if pdf_files:
        print("生成的PDF文件:")
        for i, pdf_file in enumerate(pdf_files, 1):
            file_size = os.path.getsize(pdf_file) / 1024  # KB
            print(f"{i}. {pdf_file} ({file_size:.1f} KB)")
        
        print(f"\nPDF特点:")
        print(f"• 包含完整的图表和可视化内容")
        print(f"• 保持原始HTML的交互式设计")
        print(f"• 优化的打印布局和分页")
        print(f"• 专业的页眉页脚")
    else:
        print("✗ 所有转换方法都失败了")
        print("建议使用之前生成的 ok_interactive.pdf")
    
    # 清理临时文件
    if os.path.exists('ok_print_optimized.html'):
        os.remove('ok_print_optimized.html')
        print(f"\n✓ 清理临时文件完成")

if __name__ == "__main__":
    main()
