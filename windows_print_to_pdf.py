#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Windows内置功能将HTML转换为PDF
"""

import os
import subprocess
import time

def create_print_ready_html():
    """创建适合打印的HTML版本"""
    
    # 读取原始HTML
    with open('ok.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 添加打印优化的CSS
    print_css = """
    <style>
        @media print {
            /* 确保颜色在PDF中显示 */
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            
            /* 页面设置 */
            @page {
                size: A4;
                margin: 2cm;
            }
            
            /* 隐藏导航 */
            header nav {
                display: none !important;
            }
            
            /* 优化章节分页 */
            section {
                page-break-inside: avoid;
                break-inside: avoid;
                margin-bottom: 2rem;
            }
            
            /* 确保图表容器适合页面 */
            .chart-container {
                page-break-inside: avoid;
                break-inside: avoid;
                max-height: 400px !important;
                margin: 1rem 0;
            }
            
            /* 优化表格 */
            .metric-card {
                page-break-inside: avoid;
                break-inside: avoid;
                margin-bottom: 1rem;
            }
            
            /* 确保Canvas图表可见 */
            canvas {
                max-width: 100% !important;
                height: auto !important;
                page-break-inside: avoid;
            }
            
            /* 优化字体大小 */
            .section-title {
                font-size: 1.5rem !important;
                margin-bottom: 1rem !important;
            }
            
            /* 优化间距 */
            .mb-16 {
                margin-bottom: 2rem !important;
            }
            
            /* 确保背景色显示 */
            .bg-white {
                background-color: white !important;
            }
            
            .bg-teal-600 {
                background-color: #0d9488 !important;
            }
            
            .text-white {
                color: white !important;
            }
            
            /* 优化表格显示 */
            table {
                border-collapse: collapse !important;
                width: 100% !important;
            }
            
            th, td {
                border: 1px solid #ccc !important;
                padding: 8px !important;
                text-align: center !important;
            }
            
            th {
                background-color: #0d9488 !important;
                color: white !important;
            }
        }
        
        /* 添加页面计数器 */
        @page {
            @bottom-right {
                content: "第 " counter(page) " 页";
                font-size: 10pt;
                color: #666;
            }
            
            @top-center {
                content: "人员年度工作量交互式分析报告";
                font-size: 12pt;
                color: #666;
                font-weight: bold;
            }
        }
        
        /* 确保页面在屏幕上也能正常显示 */
        body {
            font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif;
            line-height: 1.6;
        }
    </style>
    
    <script>
        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成');
            
            // 等待图表加载完成
            setTimeout(function() {
                console.log('图表应该已经加载完成');
                
                // 添加打印就绪标记
                document.body.setAttribute('data-print-ready', 'true');
                
                // 在控制台显示打印提示
                console.log('页面已准备好打印！');
                console.log('请按 Ctrl+P 打印，或在浏览器菜单中选择"打印"');
                console.log('建议选择"另存为PDF"作为打印机');
                
            }, 3000);
        });
        
        // 添加打印按钮
        window.addEventListener('load', function() {
            // 创建打印按钮
            const printButton = document.createElement('button');
            printButton.innerHTML = '🖨️ 打印/保存为PDF';
            printButton.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
                padding: 10px 20px;
                background-color: #0d9488;
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                font-size: 14px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            `;
            
            printButton.onclick = function() {
                window.print();
            };
            
            // 只在非打印模式下显示按钮
            const style = document.createElement('style');
            style.textContent = '@media print { .print-button { display: none !important; } }';
            document.head.appendChild(style);
            printButton.className = 'print-button';
            
            document.body.appendChild(printButton);
        });
    </script>
    """
    
    # 在</head>前插入打印优化CSS
    print_ready_html = html_content.replace('</head>', print_css + '\n</head>')
    
    # 保存打印就绪的HTML
    with open('ok_print_ready.html', 'w', encoding='utf-8') as f:
        f.write(print_ready_html)
    
    print("✓ 创建打印就绪HTML文件: ok_print_ready.html")
    return 'ok_print_ready.html'

def open_in_browser_for_printing(html_file):
    """在浏览器中打开HTML文件以便打印"""
    try:
        # 获取文件的绝对路径
        html_path = os.path.abspath(html_file)
        file_url = f"file:///{html_path.replace(os.sep, '/')}"
        
        print(f"正在在默认浏览器中打开: {file_url}")
        
        # 在Windows中打开默认浏览器
        os.startfile(html_path)
        
        print("\n" + "="*60)
        print("📋 打印说明:")
        print("="*60)
        print("1. 浏览器已打开HTML文件")
        print("2. 等待页面完全加载（约3秒）")
        print("3. 点击页面右上角的'🖨️ 打印/保存为PDF'按钮")
        print("   或者按 Ctrl+P 快捷键")
        print("4. 在打印对话框中:")
        print("   • 选择'Microsoft Print to PDF'作为打印机")
        print("   • 或选择'另存为PDF'选项")
        print("   • 确保选中'更多设置' > '背景图形'")
        print("5. 点击'打印'并选择保存位置")
        print("6. 建议文件名: 人员工作量分析报告.pdf")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"✗ 打开浏览器失败: {e}")
        return False

def create_batch_file():
    """创建批处理文件用于快速打印"""
    
    batch_content = f'''@echo off
echo 正在打开HTML文件进行打印...
start "" "{os.path.abspath('ok_print_ready.html')}"

echo.
echo ========================================
echo 打印说明:
echo ========================================
echo 1. 等待浏览器打开并加载完成
echo 2. 按 Ctrl+P 或点击打印按钮
echo 3. 选择 "Microsoft Print to PDF"
echo 4. 确保勾选 "背景图形" 选项
echo 5. 保存为PDF文件
echo ========================================
echo.
pause
'''
    
    with open('打印HTML到PDF.bat', 'w', encoding='gbk') as f:
        f.write(batch_content)
    
    print("✓ 创建批处理文件: 打印HTML到PDF.bat")

def main():
    """主函数"""
    print("="*60)
    print("Windows HTML转PDF工具")
    print("="*60)
    
    # 检查输入文件
    if not os.path.exists('ok.html'):
        print("✗ 错误: 找不到ok.html文件")
        return
    
    print(f"\n开始准备HTML文件用于打印...")
    
    # 创建打印就绪的HTML
    print_ready_html = create_print_ready_html()
    
    # 创建批处理文件
    create_batch_file()
    
    # 在浏览器中打开
    success = open_in_browser_for_printing(print_ready_html)
    
    if success:
        print(f"\n💡 提示:")
        print(f"• 如果需要重新打印，可以双击 '打印HTML到PDF.bat' 文件")
        print(f"• 或者直接打开 'ok_print_ready.html' 文件")
        print(f"• 生成的PDF将包含完整的图表和格式")
        print(f"• 推荐使用Chrome或Edge浏览器以获得最佳效果")
    else:
        print(f"\n✗ 自动打开失败，请手动打开 'ok_print_ready.html' 文件")

if __name__ == "__main__":
    main()
