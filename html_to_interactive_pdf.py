#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将HTML文件转换为交互式PDF
支持保留图表、链接和交互元素
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def install_required_packages():
    """安装必要的包"""
    packages = [
        'weasyprint',
        'selenium',
        'webdriver-manager',
        'reportlab',
        'PyPDF2'
    ]
    
    print("正在检查和安装必要的包...")
    for package in packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"正在安装 {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✓ {package} 安装完成")

def create_enhanced_html():
    """创建增强版HTML，优化PDF输出"""
    
    # 读取原始HTML
    with open('ok.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 添加PDF优化的CSS
    pdf_css = """
    <style>
        /* PDF优化样式 */
        @media print {
            body {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            
            .chart-container {
                page-break-inside: avoid;
                break-inside: avoid;
            }
            
            section {
                page-break-inside: avoid;
                break-inside: avoid;
                margin-bottom: 2rem;
            }
            
            .metric-card {
                page-break-inside: avoid;
                break-inside: avoid;
            }
            
            /* 确保图表在PDF中可见 */
            canvas {
                max-width: 100% !important;
                height: auto !important;
            }
            
            /* 导航在PDF中隐藏 */
            nav {
                display: none !important;
            }
            
            /* 优化字体大小 */
            .section-title {
                font-size: 1.5rem;
            }
            
            /* 页面边距 */
            @page {
                margin: 2cm;
                size: A4;
            }
        }
        
        /* 添加页面编号 */
        @page {
            @bottom-right {
                content: "第 " counter(page) " 页";
                font-size: 10pt;
                color: #666;
            }
        }
    </style>
    """
    
    # 在</head>前插入PDF优化CSS
    enhanced_html = html_content.replace('</head>', pdf_css + '\n</head>')
    
    # 保存增强版HTML
    with open('ok_enhanced.html', 'w', encoding='utf-8') as f:
        f.write(enhanced_html)
    
    print("✓ 创建增强版HTML文件: ok_enhanced.html")
    return 'ok_enhanced.html'

def convert_with_weasyprint(html_file):
    """使用WeasyPrint转换为PDF"""
    try:
        import weasyprint
        
        print("正在使用WeasyPrint转换为PDF...")
        
        # 转换为PDF
        html_doc = weasyprint.HTML(filename=html_file)
        pdf_file = 'ok_weasyprint.pdf'
        html_doc.write_pdf(pdf_file)
        
        print(f"✓ WeasyPrint转换完成: {pdf_file}")
        return pdf_file
        
    except Exception as e:
        print(f"✗ WeasyPrint转换失败: {e}")
        return None

def convert_with_selenium(html_file):
    """使用Selenium + Chrome转换为PDF"""
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        import json
        
        print("正在使用Selenium + Chrome转换为PDF...")
        
        # 设置Chrome选项
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        
        # 启动浏览器
        driver = webdriver.Chrome(options=chrome_options)
        
        try:
            # 获取绝对路径
            html_path = os.path.abspath(html_file)
            file_url = f"file:///{html_path.replace(os.sep, '/')}"
            
            # 加载页面
            driver.get(file_url)
            
            # 等待图表加载完成
            print("等待页面和图表加载...")
            time.sleep(5)
            
            # 等待Chart.js图表加载
            try:
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "canvas"))
                )
                time.sleep(3)  # 额外等待图表渲染
            except:
                print("警告: 图表可能未完全加载")
            
            # 设置PDF打印选项
            pdf_options = {
                'landscape': False,
                'displayHeaderFooter': True,
                'headerTemplate': '<div style="font-size:10px; text-align:center; width:100%;">人员年度工作量交互式分析报告</div>',
                'footerTemplate': '<div style="font-size:10px; text-align:center; width:100%;"><span class="pageNumber"></span> / <span class="totalPages"></span></div>',
                'printBackground': True,
                'format': 'A4',
                'margin': {
                    'top': '1in',
                    'bottom': '1in',
                    'left': '0.5in',
                    'right': '0.5in'
                }
            }
            
            # 生成PDF
            pdf_data = driver.execute_cdp_cmd('Page.printToPDF', pdf_options)
            
            # 保存PDF
            pdf_file = 'ok_selenium.pdf'
            with open(pdf_file, 'wb') as f:
                import base64
                f.write(base64.b64decode(pdf_data['data']))
            
            print(f"✓ Selenium转换完成: {pdf_file}")
            return pdf_file
            
        finally:
            driver.quit()
            
    except Exception as e:
        print(f"✗ Selenium转换失败: {e}")
        return None

def create_interactive_pdf_with_reportlab():
    """使用ReportLab创建交互式PDF"""
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.lib.colors import HexColor
        from reportlab.platypus import Table, TableStyle
        from reportlab.lib import colors
        
        print("正在使用ReportLab创建交互式PDF...")
        
        # 创建PDF文档
        pdf_file = 'ok_interactive.pdf'
        doc = SimpleDocTemplate(pdf_file, pagesize=A4, 
                              rightMargin=72, leftMargin=72,
                              topMargin=72, bottomMargin=18)
        
        # 获取样式
        styles = getSampleStyleSheet()
        
        # 自定义样式
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            textColor=HexColor('#1e293b'),
            alignment=1  # 居中
        )
        
        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=12,
            textColor=HexColor('#0d9488'),
            borderWidth=1,
            borderColor=HexColor('#0d9488'),
            borderPadding=5
        )
        
        content_style = ParagraphStyle(
            'CustomContent',
            parent=styles['Normal'],
            fontSize=11,
            spaceAfter=12,
            textColor=HexColor('#334155')
        )
        
        # 构建PDF内容
        story = []
        
        # 标题
        story.append(Paragraph("人员年度工作量交互式分析报告", title_style))
        story.append(Spacer(1, 20))
        
        # 核心概览部分
        story.append(Paragraph("核心概览：行政与非行政人员工作量对比", heading_style))
        story.append(Spacer(1, 12))
        
        story.append(Paragraph(
            "本部分旨在快速揭示行政与非行政人员在工作负荷上的核心差异。通过关键指标和可视化图表的对比，"
            "您可以直观地了解两类岗位在工作量、工作模式及运营贡献上的根本不同。",
            content_style
        ))
        
        # 关键指标表格
        data = [
            ['指标', '数值', '说明'],
            ['人均年工作量差异', '9.5% 更高', '非行政人员（1779小时）vs 行政人员（1625小时）'],
            ['运营模式', '24/7 连续运转', '非行政岗位需保障全天候不间断服务'],
            ['工作复杂度', '>20% 为顶班工时', '包含大量顶班、培训等辅助任务']
        ]
        
        table = Table(data, colWidths=[2*inch, 2*inch, 3*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor('#0d9488')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), HexColor('#f8fafc')),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(table)
        story.append(Spacer(1, 20))
        
        # 工作量分解部分
        story.append(PageBreak())
        story.append(Paragraph("深度分解：非行政人员工作量构成", heading_style))
        story.append(Spacer(1, 12))
        
        story.append(Paragraph(
            "非行政人员的工作远不止于常规值守。本节将深入剖析其年度总工作量的具体构成，"
            "揭示各项任务的占比。请特别关注顶班部分，它是保障24/7运营连续性和员工福祉的关键缓冲。",
            content_style
        ))
        
        # 工作量构成表格
        breakdown_data = [
            ['工作类型', '工时（小时）', '占比'],
            ['工作日值守', '8,500', '59.7%'],
            ['节假日值守', '2,784', '19.6%'],
            ['顶班', '2,880', '20.2%'],
            ['会议培训', '36', '0.3%'],
            ['党建学习', '24', '0.2%'],
            ['训练演练', '9', '0.1%'],
            ['总计', '14,233', '100%']
        ]
        
        breakdown_table = Table(breakdown_data, colWidths=[2.5*inch, 2*inch, 1.5*inch])
        breakdown_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor('#0d9488')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -2), HexColor('#f8fafc')),
            ('BACKGROUND', (0, -1), (-1, -1), HexColor('#e2e8f0')),
            ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(breakdown_table)
        story.append(Spacer(1, 20))
        
        # 人员配置论证部分
        story.append(PageBreak())
        story.append(Paragraph("核心论证：为何需要8名非行政人员？", heading_style))
        story.append(Spacer(1, 12))
        
        story.append(Paragraph(
            "8人配置并非随意设定，而是基于总工作需求和可持续工作负荷的科学计算结果。",
            content_style
        ))
        
        # 计算公式
        formula_data = [
            ['计算步骤', '数值', '说明'],
            ['年度总工作需求', '14,233 小时', '值守+培训+顶班等'],
            ['人均可持续年工时', '1,779 小时', '平衡高强度与休息'],
            ['计算公式', '14,233 ÷ 1,779', '总需求 ÷ 人均承载'],
            ['最低人员配置', '8 人', '科学验证的必要数量']
        ]
        
        formula_table = Table(formula_data, colWidths=[2.5*inch, 2*inch, 2.5*inch])
        formula_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor('#0d9488')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -2), HexColor('#f8fafc')),
            ('BACKGROUND', (0, -1), (-1, -1), HexColor('#14b8a6')),
            ('TEXTCOLOR', (0, -1), (-1, -1), colors.whitesmoke),
            ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(formula_table)
        story.append(Spacer(1, 20))
        
        # 论证逻辑
        story.append(Paragraph("论证逻辑：", ParagraphStyle(
            'LogicHeading',
            parent=styles['Heading3'],
            fontSize=12,
            spaceAfter=8,
            textColor=HexColor('#1e293b')
        )))
        
        logic_points = [
            "✔ <b>保障24/7覆盖：</b>14,233小时的总工作量远超任何个人能力，必须由团队完成。8人配置是实现无间断轮班和节假日值守的基础。",
            "✔ <b>吸收必要缺勤：</b>顶班所需的2880小时，加上培训、休假等，必须由足够的人员储备来分摊。减少人员将导致服务中断或现有员工过度劳累。",
            "✔ <b>维持工作负荷平衡：</b>1779小时的人均年工作量是高强度轮班工作下的合理负荷。少于8人将推高人均工时，引发疲劳、效率下降和安全风险。"
        ]
        
        for point in logic_points:
            story.append(Paragraph(point, content_style))
            story.append(Spacer(1, 8))
        
        # 结论部分
        story.append(PageBreak())
        story.append(Paragraph("结论与建议", heading_style))
        story.append(Spacer(1, 12))
        
        story.append(Paragraph(
            "<b>核心结论：</b>数据分析明确证明，非行政人员的工作负荷不仅在数量上（高出9.5%），"
            "更在性质上（24/7轮班、高强度、高复杂度）远超行政人员。当前<b>8名非行政人员的配置是经过科学计算验证的最低需求</b>，"
            "是保障组织核心业务不间断运行、维持运营韧性、并确保员工福祉的基石。",
            content_style
        ))
        
        story.append(Spacer(1, 12))
        
        story.append(Paragraph("<b>管理建议：</b>", content_style))
        
        recommendations = [
            "• <b>维持当前配置：</b>坚决维持8名非行政人员的配置，将其视为保障组织核心竞争力的战略性投资。",
            "• <b>定期工作量审查：</b>建立年度工作量审查机制，动态评估运营需求，确保人员配置始终与实际情况相匹配。",
            "• <b>加强员工关怀：</b>针对非行政人员高强度的工作特性，提供专项的疲劳管理和心理健康支持。"
        ]
        
        for rec in recommendations:
            story.append(Paragraph(rec, content_style))
            story.append(Spacer(1, 8))
        
        # 生成PDF
        doc.build(story)
        
        print(f"✓ ReportLab交互式PDF创建完成: {pdf_file}")
        return pdf_file
        
    except Exception as e:
        print(f"✗ ReportLab转换失败: {e}")
        return None

def main():
    """主函数"""
    print("="*60)
    print("HTML转交互式PDF转换工具")
    print("="*60)
    
    # 检查输入文件
    if not os.path.exists('ok.html'):
        print("✗ 错误: 找不到ok.html文件")
        return
    
    # 安装必要的包
    install_required_packages()
    
    print(f"\n开始转换HTML文件为交互式PDF...")
    
    # 创建增强版HTML
    enhanced_html = create_enhanced_html()
    
    # 尝试多种转换方法
    pdf_files = []
    
    # 方法1: ReportLab (最佳交互性)
    pdf_file = create_interactive_pdf_with_reportlab()
    if pdf_file:
        pdf_files.append(pdf_file)
    
    # 方法2: Selenium + Chrome (保持图表)
    pdf_file = convert_with_selenium(enhanced_html)
    if pdf_file:
        pdf_files.append(pdf_file)
    
    # 方法3: WeasyPrint (备选方案)
    pdf_file = convert_with_weasyprint(enhanced_html)
    if pdf_file:
        pdf_files.append(pdf_file)
    
    # 总结
    print(f"\n" + "="*60)
    print("转换完成！")
    print("="*60)
    
    if pdf_files:
        print("生成的PDF文件:")
        for i, pdf_file in enumerate(pdf_files, 1):
            file_size = os.path.getsize(pdf_file) / 1024  # KB
            print(f"{i}. {pdf_file} ({file_size:.1f} KB)")
        
        print(f"\n推荐使用:")
        print(f"• ok_interactive.pdf - 最佳交互性和格式")
        print(f"• ok_selenium.pdf - 保持原始图表样式")
        print(f"• ok_weasyprint.pdf - 轻量级备选方案")
    else:
        print("✗ 所有转换方法都失败了")
    
    # 清理临时文件
    if os.path.exists('ok_enhanced.html'):
        os.remove('ok_enhanced.html')
        print(f"\n✓ 清理临时文件完成")

if __name__ == "__main__":
    main()
