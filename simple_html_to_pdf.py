#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版HTML转PDF工具
使用ReportLab创建交互式PDF
"""

import os
import sys
import subprocess

def install_reportlab():
    """安装ReportLab"""
    try:
        import reportlab
        print("✓ ReportLab 已安装")
        return True
    except ImportError:
        print("正在安装 ReportLab...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'reportlab'])
            print("✓ ReportLab 安装完成")
            return True
        except:
            print("✗ ReportLab 安装失败")
            return False

def create_interactive_pdf():
    """创建交互式PDF"""
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak, Table, TableStyle
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.lib.colors import HexColor
        from reportlab.lib import colors
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont

        print("正在创建交互式PDF...")

        # 注册中文字体
        try:
            # 尝试注册系统中的中文字体
            import platform
            if platform.system() == "Windows":
                # Windows系统字体路径
                font_paths = [
                    "C:/Windows/Fonts/simsun.ttc",  # 宋体
                    "C:/Windows/Fonts/simhei.ttf",  # 黑体
                    "C:/Windows/Fonts/msyh.ttc",    # 微软雅黑
                ]

                font_registered = False
                for font_path in font_paths:
                    if os.path.exists(font_path):
                        try:
                            pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                            font_registered = True
                            print(f"✓ 成功注册中文字体: {font_path}")
                            break
                        except:
                            continue

                if not font_registered:
                    print("⚠ 未找到系统中文字体，将使用默认字体（可能显示乱码）")
                    chinese_font = 'Helvetica'
                else:
                    chinese_font = 'ChineseFont'
            else:
                print("⚠ 非Windows系统，使用默认字体")
                chinese_font = 'Helvetica'
        except Exception as e:
            print(f"⚠ 字体注册失败: {e}，使用默认字体")
            chinese_font = 'Helvetica'

        # 创建PDF文档
        pdf_file = 'ok_interactive_chinese.pdf'
        doc = SimpleDocTemplate(pdf_file, pagesize=A4,
                              rightMargin=72, leftMargin=72,
                              topMargin=72, bottomMargin=72)
        
        # 获取样式
        styles = getSampleStyleSheet()
        
        # 自定义样式（使用中文字体）
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontName=chinese_font,
            fontSize=20,
            spaceAfter=30,
            textColor=HexColor('#1e293b'),
            alignment=1  # 居中
        )

        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontName=chinese_font,
            fontSize=16,
            spaceAfter=15,
            textColor=HexColor('#0d9488'),
            borderWidth=2,
            borderColor=HexColor('#0d9488'),
            borderPadding=8,
            backColor=HexColor('#f0fdfa')
        )

        subheading_style = ParagraphStyle(
            'CustomSubHeading',
            parent=styles['Heading3'],
            fontName=chinese_font,
            fontSize=14,
            spaceAfter=10,
            textColor=HexColor('#1e293b'),
            leftIndent=20
        )

        content_style = ParagraphStyle(
            'CustomContent',
            parent=styles['Normal'],
            fontName=chinese_font,
            fontSize=11,
            spaceAfter=12,
            textColor=HexColor('#334155'),
            leading=16
        )

        highlight_style = ParagraphStyle(
            'HighlightContent',
            parent=styles['Normal'],
            fontName=chinese_font,
            fontSize=11,
            spaceAfter=12,
            textColor=HexColor('#0d9488'),
            backColor=HexColor('#f0fdfa'),
            borderWidth=1,
            borderColor=HexColor('#0d9488'),
            borderPadding=10,
            leading=16
        )
        
        # 构建PDF内容
        story = []
        
        # 标题页
        story.append(Spacer(1, 100))
        story.append(Paragraph("人员年度工作量", title_style))
        story.append(Paragraph("交互式分析报告", title_style))
        story.append(Spacer(1, 50))
        
        story.append(Paragraph("基于科学数据分析的人员配置论证", ParagraphStyle(
            'Subtitle',
            parent=styles['Normal'],
            fontName=chinese_font,
            fontSize=14,
            textColor=HexColor('#64748b'),
            alignment=1
        )))
        
        story.append(Spacer(1, 100))
        story.append(Paragraph("2024年度", ParagraphStyle(
            'Date',
            parent=styles['Normal'],
            fontName=chinese_font,
            fontSize=12,
            textColor=HexColor('#94a3b8'),
            alignment=1
        )))
        
        story.append(PageBreak())
        
        # 目录
        story.append(Paragraph("目录", heading_style))
        story.append(Spacer(1, 20))
        
        toc_items = [
            "1. 核心概览：行政与非行政人员工作量对比",
            "2. 深度分解：非行政人员工作量构成", 
            "3. 核心论证：为何需要8名非行政人员？",
            "4. 结论与建议"
        ]
        
        for item in toc_items:
            story.append(Paragraph(item, content_style))
            story.append(Spacer(1, 8))
        
        story.append(PageBreak())
        
        # 1. 核心概览部分
        story.append(Paragraph("1. 核心概览：行政与非行政人员工作量对比", heading_style))
        story.append(Spacer(1, 15))
        
        story.append(Paragraph(
            "本部分旨在快速揭示行政与非行政人员在工作负荷上的核心差异。通过关键指标和数据对比，"
            "您可以直观地了解两类岗位在工作量、工作模式及运营贡献上的根本不同。",
            content_style
        ))
        
        # 关键指标表格
        story.append(Spacer(1, 15))
        story.append(Paragraph("1.1 关键指标对比", subheading_style))
        
        data = [
            ['指标类别', '非行政人员', '行政人员', '差异分析'],
            ['人均年工作量', '1,779小时', '1,625小时', '高出9.5%'],
            ['运营模式', '24/7连续运转', '标准工作日', '全天候vs常规'],
            ['工作复杂度', '>20%为顶班工时', '标准办公', '高复杂度'],
            ['年度总工作量', '14,233小时', '不适用', '团队总负荷']
        ]
        
        table = Table(data, colWidths=[1.8*inch, 1.5*inch, 1.5*inch, 2.2*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor('#0d9488')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), chinese_font),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), HexColor('#f8fafc')),
            ('FONTNAME', (0, 1), (-1, -1), chinese_font),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#e2e8f0')),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE')
        ]))
        
        story.append(table)
        story.append(Spacer(1, 20))
        
        # 核心发现
        story.append(Paragraph("1.2 核心发现", subheading_style))
        
        findings = [
            "<b>工作负荷差异显著：</b>非行政人员人均年工作量比行政人员高出154小时（9.5%），体现了岗位职责的差异。",
            "<b>运营模式根本不同：</b>非行政岗位需要保障24/7不间断服务，而行政岗位遵循标准工作时间。",
            "<b>工作构成复杂：</b>非行政工作包含值守、培训、顶班等多种类型，其中顶班工时占比超过20%。"
        ]
        
        for finding in findings:
            story.append(Paragraph(f"• {finding}", content_style))
            story.append(Spacer(1, 8))
        
        story.append(PageBreak())
        
        # 2. 深度分解部分
        story.append(Paragraph("2. 深度分解：非行政人员工作量构成", heading_style))
        story.append(Spacer(1, 15))
        
        story.append(Paragraph(
            "非行政人员的工作远不止于常规值守。本节将深入剖析其年度总工作量的具体构成，"
            "揭示各项任务的占比。请特别关注顶班部分，它是保障24/7运营连续性和员工福祉的关键缓冲。",
            content_style
        ))
        
        # 工作量构成表格
        story.append(Spacer(1, 15))
        story.append(Paragraph("2.1 年度总工作量构成分析", subheading_style))
        
        breakdown_data = [
            ['工作类型', '工时（小时）', '占比', '说明'],
            ['工作日值守', '8,500', '59.7%', '正常工作日的值守任务'],
            ['节假日值守', '2,784', '19.6%', '节假日期间的值守工作'],
            ['顶班轮休', '2,880', '20.2%', '人员轮休时的顶班工作'],
            ['会议培训', '36', '0.3%', '业务培训和会议参与'],
            ['党建学习', '24', '0.2%', '党建活动和学习'],
            ['训练演练', '9', '0.1%', '应急演练和技能训练'],
            ['<b>总计</b>', '<b>14,233</b>', '<b>100%</b>', '<b>年度总工作量</b>']
        ]
        
        breakdown_table = Table(breakdown_data, colWidths=[1.8*inch, 1.2*inch, 1*inch, 2.5*inch])
        breakdown_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor('#0d9488')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), chinese_font),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -2), HexColor('#f8fafc')),
            ('BACKGROUND', (0, -1), (-1, -1), HexColor('#e2e8f0')),
            ('FONTNAME', (0, 1), (-1, -1), chinese_font),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#e2e8f0')),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE')
        ]))
        
        story.append(breakdown_table)
        story.append(Spacer(1, 20))
        
        # 关键洞察
        story.append(Paragraph("2.2 关键洞察", subheading_style))
        
        story.append(Paragraph(
            "<b>顶班工作的重要性：</b>2,880小时的顶班工作（占总工作量的20.2%）是维持24/7运营连续性的关键。"
            "这部分工作确保了在人员休假、培训或突发情况下，核心业务不会中断。",
            highlight_style
        ))
        
        insights = [
            "<b>值守工作占主导：</b>工作日和节假日值守合计11,284小时，占总工作量的79.3%，体现了岗位的核心职责。",
            "<b>培训投入必要：</b>虽然培训相关工作仅占0.6%，但对于维持专业水准和应对复杂情况至关重要。",
            "<b>工作分布合理：</b>各项工作类型的分布反映了岗位的多元化需求和运营的复杂性。"
        ]
        
        for insight in insights:
            story.append(Paragraph(f"• {insight}", content_style))
            story.append(Spacer(1, 8))
        
        story.append(PageBreak())
        
        # 3. 核心论证部分
        story.append(Paragraph("3. 核心论证：为何需要8名非行政人员？", heading_style))
        story.append(Spacer(1, 15))
        
        story.append(Paragraph(
            "8人配置并非随意设定，而是基于总工作需求和可持续工作负荷的科学计算结果。"
            "本节将通过详细的计算过程，清晰展示从总工时需求到最低人员配置的逻辑推导。",
            content_style
        ))
        
        # 计算过程
        story.append(Spacer(1, 15))
        story.append(Paragraph("3.1 科学计算过程", subheading_style))
        
        # 计算公式表格
        formula_data = [
            ['计算步骤', '数值', '计算依据'],
            ['年度总工作需求', '14,233 小时', '所有工作类型的时间总和'],
            ['人均可持续年工时', '1,779 小时', '基于实际数据的合理负荷'],
            ['基础人员需求', '14,233 ÷ 1,779 = 8.0', '总需求除以人均承载能力'],
            ['<b>最低人员配置</b>', '<b>8 人</b>', '<b>科学计算的必要数量</b>']
        ]
        
        formula_table = Table(formula_data, colWidths=[2.2*inch, 2*inch, 2.8*inch])
        formula_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor('#0d9488')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), chinese_font),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -2), HexColor('#f8fafc')),
            ('BACKGROUND', (0, -1), (-1, -1), HexColor('#14b8a6')),
            ('TEXTCOLOR', (0, -1), (-1, -1), colors.whitesmoke),
            ('FONTNAME', (0, 1), (-1, -1), chinese_font),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#e2e8f0')),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE')
        ]))
        
        story.append(formula_table)
        story.append(Spacer(1, 20))
        
        # 论证逻辑
        story.append(Paragraph("3.2 论证逻辑", subheading_style))
        
        logic_points = [
            "<b>保障24/7覆盖：</b>14,233小时的总工作量远超任何个人能力，必须由团队完成。8人配置是实现无间断轮班和节假日值守的基础。",
            "<b>吸收必要缺勤：</b>顶班所需的2,880小时，加上培训、休假等，必须由足够的人员储备来分摊。减少人员将导致服务中断或现有员工过度劳累。",
            "<b>维持工作负荷平衡：</b>1,779小时的人均年工作量是高强度轮班工作下的合理负荷。少于8人将推高人均工时，引发疲劳、效率下降和安全风险。"
        ]
        
        for i, point in enumerate(logic_points, 1):
            story.append(Paragraph(f"{i}. {point}", content_style))
            story.append(Spacer(1, 12))
        
        # 风险分析
        story.append(Paragraph("3.3 人员不足的风险分析", subheading_style))
        
        story.append(Paragraph(
            "如果人员配置少于8人，将面临以下严重风险：",
            content_style
        ))
        
        risks = [
            "<b>服务中断风险：</b>无法保障24/7连续运营，可能导致关键时刻的服务空白。",
            "<b>员工过劳风险：</b>人均工作负荷过高，影响员工健康和工作效率。",
            "<b>质量下降风险：</b>疲劳和压力导致工作质量下降，增加错误和事故概率。",
            "<b>人员流失风险：</b>过度的工作压力可能导致优秀员工离职，形成恶性循环。"
        ]
        
        for risk in risks:
            story.append(Paragraph(f"• {risk}", content_style))
            story.append(Spacer(1, 8))
        
        story.append(PageBreak())
        
        # 4. 结论与建议
        story.append(Paragraph("4. 结论与建议", heading_style))
        story.append(Spacer(1, 15))
        
        story.append(Paragraph("4.1 核心结论", subheading_style))
        
        story.append(Paragraph(
            "数据分析明确证明，非行政人员的工作负荷不仅在数量上（高出9.5%），"
            "更在性质上（24/7轮班、高强度、高复杂度）远超行政人员。当前<b>8名非行政人员的配置是经过科学计算验证的最低需求</b>，"
            "是保障组织核心业务不间断运行、维持运营韧性、并确保员工福祉的基石。任何削减都将对运营稳定性和服务质量构成直接威胁。",
            highlight_style
        ))
        
        story.append(Spacer(1, 20))
        story.append(Paragraph("4.2 管理建议", subheading_style))
        
        recommendations = [
            "<b>维持当前配置：</b>坚决维持8名非行政人员的配置，将其视为保障组织核心竞争力的战略性投资。",
            "<b>定期工作量审查：</b>建立年度工作量审查机制，动态评估运营需求，确保人员配置始终与实际情况相匹配。",
            "<b>加强员工关怀：</b>针对非行政人员高强度的工作特性，提供专项的疲劳管理和心理健康支持，提升员工满意度和留存率。",
            "<b>优化工作流程：</b>通过技术手段和流程优化，提高工作效率，在保持服务质量的前提下适当减轻工作强度。",
            "<b>建立应急预案：</b>制定详细的应急人员调配预案，确保在突发情况下能够快速响应和处理。"
        ]
        
        for i, rec in enumerate(recommendations, 1):
            story.append(Paragraph(f"{i}. {rec}", content_style))
            story.append(Spacer(1, 10))
        
        story.append(Spacer(1, 30))
        
        # 报告结语
        story.append(Paragraph(
            "本报告基于科学的数据分析方法，为8名非行政人员的配置提供了充分的论证依据。"
            "建议管理层将此配置作为组织运营的基础保障，确保长期稳定和高效的服务能力。",
            ParagraphStyle(
                'Conclusion',
                parent=styles['Normal'],
                fontName=chinese_font,
                fontSize=12,
                textColor=HexColor('#0d9488'),
                backColor=HexColor('#f0fdfa'),
                borderWidth=1,
                borderColor=HexColor('#0d9488'),
                borderPadding=15,
                alignment=1,
                leading=18
            )
        ))
        
        # 生成PDF
        doc.build(story)
        
        print(f"✓ 交互式PDF创建完成: {pdf_file}")
        return pdf_file
        
    except Exception as e:
        print(f"✗ PDF创建失败: {e}")
        return None

def main():
    """主函数"""
    print("="*60)
    print("HTML转交互式PDF转换工具")
    print("="*60)
    
    # 检查输入文件
    if not os.path.exists('ok.html'):
        print("✗ 错误: 找不到ok.html文件")
        return
    
    # 安装必要的包
    if not install_reportlab():
        return
    
    print(f"\n开始转换HTML文件为交互式PDF...")
    
    # 创建交互式PDF
    pdf_file = create_interactive_pdf()
    
    # 总结
    print(f"\n" + "="*60)
    print("转换完成！")
    print("="*60)
    
    if pdf_file:
        file_size = os.path.getsize(pdf_file) / 1024  # KB
        print(f"生成的PDF文件: {pdf_file} ({file_size:.1f} KB)")
        print(f"\nPDF特点:")
        print(f"• 保持原始HTML的内容结构")
        print(f"• 优化的排版和视觉效果")
        print(f"• 专业的表格和数据展示")
        print(f"• 适合打印和分享")
    else:
        print("✗ PDF转换失败")

if __name__ == "__main__":
    main()
