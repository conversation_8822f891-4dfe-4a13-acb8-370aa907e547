#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建工作量分析图表
"""

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from matplotlib import rcParams

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def create_workload_charts():
    """创建工作量分析图表"""
    
    # 基于分析结果的数据
    non_admin_stats = {
        '总工作量': 21184.04,
        '平均工作量': 365.24,
        '中位数': 13.50,
        '标准差': 1250.81,
        '最大值': 8500.00,
        '最小值': 1.00,
        '数据点数': 58
    }
    
    # 人员配备数据
    staff_data = {
        '估算当前人员': 29,
        '基础最少需求': 72.5,
        '建议配备': 96,
        '8人要求': 8
    }
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('人员年度工作量统计分析报告', fontsize=18, fontweight='bold')
    
    # 1. 工作量统计指标
    ax1 = axes[0, 0]
    metrics = ['平均工作量', '中位数', '最大值', '最小值']
    values = [non_admin_stats[m] for m in metrics]
    
    bars = ax1.bar(metrics, values, color=['skyblue', 'lightgreen', 'coral', 'gold'])
    ax1.set_title('非行政人员工作量统计指标', fontsize=14, fontweight='bold')
    ax1.set_ylabel('工作量')
    
    # 添加数值标签
    for bar, value in zip(bars, values):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(values)*0.01,
                f'{value:.1f}', ha='center', va='bottom', fontweight='bold')
    
    # 2. 人员配备需求对比
    ax2 = axes[0, 1]
    staff_categories = ['当前估算', '基础需求', '建议配备', '8人要求']
    staff_values = [staff_data['估算当前人员'], staff_data['基础最少需求'], 
                   staff_data['建议配备'], staff_data['8人要求']]
    
    colors = ['lightcoral', 'orange', 'lightgreen', 'red']
    bars = ax2.bar(staff_categories, staff_values, color=colors)
    ax2.set_title('人员配备需求分析', fontsize=14, fontweight='bold')
    ax2.set_ylabel('人员数量')
    
    # 添加数值标签
    for bar, value in zip(bars, staff_values):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(staff_values)*0.01,
                f'{value:.0f}人', ha='center', va='bottom', fontweight='bold')
    
    # 3. 工作负荷分析饼图
    ax3 = axes[1, 0]
    
    # 模拟工作负荷分布
    workload_categories = ['值守工作', '会议培训', '党建学习', '训练演练', '顶班轮休']
    workload_values = [8500+2784, 36, 24, 9, 2880]  # 基于提取的数据
    
    colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99', '#ff99cc']
    wedges, texts, autotexts = ax3.pie(workload_values, labels=workload_categories, 
                                      autopct='%1.1f%%', colors=colors, startangle=90)
    ax3.set_title('非行政人员工作量构成', fontsize=14, fontweight='bold')
    
    # 4. 人员配备论证图
    ax4 = axes[1, 1]
    
    # 计算过程可视化
    calculation_steps = ['总工作量', '合理负荷标准', '基础需求', '效率缓冲', '应急缓冲', '最终建议']
    calculation_values = [21184.04, 292.19, 72.5, 87.0, 95.7, 96]
    
    # 使用不同颜色表示计算步骤
    step_colors = ['lightblue', 'yellow', 'orange', 'lightgreen', 'lightcoral', 'red']
    
    # 创建水平条形图
    y_pos = np.arange(len(calculation_steps))
    bars = ax4.barh(y_pos, calculation_values, color=step_colors)
    
    ax4.set_yticks(y_pos)
    ax4.set_yticklabels(calculation_steps)
    ax4.set_xlabel('数值')
    ax4.set_title('人员配备计算过程', fontsize=14, fontweight='bold')
    
    # 添加数值标签
    for i, (bar, value) in enumerate(zip(bars, calculation_values)):
        if i < 2:  # 前两个显示原值
            ax4.text(bar.get_width() + max(calculation_values)*0.01, bar.get_y() + bar.get_height()/2,
                    f'{value:.1f}', ha='left', va='center', fontweight='bold')
        else:  # 后面的显示人数
            ax4.text(bar.get_width() + max(calculation_values)*0.01, bar.get_y() + bar.get_height()/2,
                    f'{value:.0f}人', ha='left', va='center', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('人员工作量分析图表.png', dpi=300, bbox_inches='tight')
    print("✓ 图表已保存为: 人员工作量分析图表.png")
    
    # 不显示图表，避免阻塞
    plt.close()

def create_summary_table():
    """创建汇总表格"""
    
    # 创建汇总数据
    summary_data = {
        '分析项目': [
            '数据来源',
            '数据规模',
            '非行政人员总工作量',
            '非行政人员平均工作量',
            '估算当前人员数',
            '当前人均负荷',
            '合理负荷标准',
            '基础最少需求',
            '建议配备人员',
            '是否支持8人要求'
        ],
        '数值/结果': [
            '人员年度工作量统计表.xlsx',
            '16行 × 10列',
            '21,184.04',
            '365.24',
            '29人',
            '730.48',
            '292.19',
            '72.5人',
            '96人',
            '✓ 支持'
        ],
        '说明': [
            'Excel文件数据',
            '包含表一和表二',
            '所有工作量数据总和',
            '基于58个数据点计算',
            '基于数据点数保守估算',
            '总工作量/当前人员数',
            '平均工作量的80%',
            '总工作量/合理负荷标准',
            '考虑效率和应急因素',
            '远超8人最低要求'
        ]
    }
    
    # 创建DataFrame
    df = pd.DataFrame(summary_data)
    
    # 保存为CSV
    df.to_csv('人员工作量分析汇总表.csv', index=False, encoding='utf-8-sig')
    print("✓ 汇总表已保存为: 人员工作量分析汇总表.csv")
    
    return df

def main():
    """主函数"""
    print("正在创建分析图表和汇总表...")
    
    # 创建图表
    create_workload_charts()
    
    # 创建汇总表
    summary_df = create_summary_table()
    
    print("\n汇总表内容:")
    print(summary_df.to_string(index=False))
    
    print(f"\n" + "="*60)
    print("图表和汇总表创建完成！")
    print("="*60)
    print("生成的文件:")
    print("- 人员工作量分析图表.png: 可视化分析图表")
    print("- 人员工作量分析汇总表.csv: 数据汇总表")

if __name__ == "__main__":
    main()
